import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

// Normalize brand name to prevent case-sensitive duplicates
async function normalizeBrand(brand: string | null, supabase: Awaited<ReturnType<typeof createClient>>): Promise<string | null> {
  if (!brand || typeof brand !== 'string') {
    return null
  }

  // Trim whitespace
  const trimmedBrand = brand.trim()
  if (!trimmedBrand) {
    return null
  }

  // Check if a similar brand already exists (case-insensitive)
  const { data: existingBrands } = await supabase
    .from('products')
    .select('brand')
    .not('brand', 'is', null)

  if (existingBrands) {
    // Find existing brand with same name (case-insensitive)
    const existingBrand = existingBrands.find((p: { brand: string | null }) =>
      p.brand && p.brand.toLowerCase().trim() === trimmedBrand.toLowerCase()
    )

    if (existingBrand) {
      // Use the existing brand's exact casing
      return existingBrand.brand.trim()
    }
  }

  // Return the trimmed brand with original casing if no match found
  return trimmedBrand
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  try {
    const supabase = await createClient()
    
    // Check if user is admin
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json(
        { error: 'Nicht autorisiert' },
        { status: 401 }
      )
    }

    const { data: profile } = await supabase
      .from('users')
      .select('is_admin')
      .eq('id', user.id)
      .single()

    if (!profile?.is_admin) {
      return NextResponse.json(
        { error: 'Nicht autorisiert' },
        { status: 403 }
      )
    }

    // Get product
    const { data: product, error } = await supabase
      .from('products')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      console.error('Error fetching product:', error)
      return NextResponse.json(
        { error: 'Produkt nicht gefunden' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      product
    })
  } catch (error) {
    console.error('Error in product GET:', error)
    return NextResponse.json(
      { error: 'Interner Serverfehler' },
      { status: 500 }
    )
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  try {
    const updates = await request.json()
    
    const supabase = await createClient()
    
    // Check if user is admin
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json(
        { error: 'Nicht autorisiert' },
        { status: 401 }
      )
    }

    const { data: profile } = await supabase
      .from('users')
      .select('is_admin')
      .eq('id', user.id)
      .single()

    if (!profile?.is_admin) {
      return NextResponse.json(
        { error: 'Nicht autorisiert' },
        { status: 403 }
      )
    }

    // Clean up empty string values for enum and UUID fields
    if (updates.coffee_type === '') {
      updates.coffee_type = null
    }
    if (updates.brand === '') {
      updates.brand = null
    }
    if (updates.blend === '') {
      updates.blend = null
    }
    if (updates.template_id === '') {
      updates.template_id = null
    }
    if (updates.title_template === '') {
      updates.title_template = null
    }

    // Convert discount_price of 0 to null to prevent showing "CHF 0.00"
    if (updates.discount_price === 0 || updates.discount_price === '0') {
      updates.discount_price = null
    }

    // Handle category-specific field validation and cleanup
    if (updates.category === 'accessories') {
      // For accessories, clear coffee-specific fields
      updates.coffee_type = null
      updates.blend = null
      updates.pack_quantity = null
      updates.pack_weight_grams = null
      updates.cost_per_espresso = null
      updates.machine_compatibility = null
    } else if (updates.category === 'coffee') {
      // For coffee products, ensure coffee_type is provided
      if (!updates.coffee_type) {
        return NextResponse.json(
          { error: 'Tipo di caffè è richiesto per i prodotti della categoria caffè' },
          { status: 400 }
        )
      }
    }

    // Normalize brand name to prevent case-sensitive duplicates
    updates.brand = await normalizeBrand(updates.brand, supabase)

    // Automatically compute cost_per_espresso if missing and category is coffee
    if (updates.category !== 'accessories' && updates.cost_per_espresso === undefined) {
      const coffeeType = updates.coffee_type
      const price = updates.price
      const discountPrice = updates.discount_price
      const packQty = updates.pack_quantity
      const packWeight = updates.pack_weight_grams

      // Use discount price if available, otherwise use regular price
      const effectivePrice = discountPrice && discountPrice > 0 ? discountPrice : price

      let computed: number | null = null
      if ((coffeeType === 'capsules' || coffeeType === 'pods') && effectivePrice && packQty) {
        computed = parseFloat((effectivePrice / packQty).toFixed(4))
      } else if ((coffeeType === 'beans' || coffeeType === 'ground') && effectivePrice && packWeight) {
        const espressoCount = packWeight / 7.5
        if (espressoCount > 0) {
          computed = parseFloat((effectivePrice / espressoCount).toFixed(4))
        }
      }
      if (computed !== null) {
        updates.cost_per_espresso = computed
      }
    }

    // Update slug if title changed
    if (updates.title) {
      updates.slug = updates.title
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '')
    }

    // Update product
    const { data: product, error } = await supabase
      .from('products')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('Error updating product:', error)
      return NextResponse.json(
        { error: 'Fehler beim Aktualisieren des Produkts' },
        { status: 500 }
      )
    }

    return NextResponse.json({ success: true, product })
  } catch (error) {
    console.error('Error in product update:', error)
    return NextResponse.json(
      { error: 'Interner Serverfehler' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  try {
    const supabase = await createClient()
    
    // Check if user is admin
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json(
        { error: 'Nicht autorisiert' },
        { status: 401 }
      )
    }

    const { data: profile } = await supabase
      .from('users')
      .select('is_admin')
      .eq('id', user.id)
      .single()

    if (!profile?.is_admin) {
      return NextResponse.json(
        { error: 'Nicht autorisiert' },
        { status: 403 }
      )
    }

    // Check if product is used in any orders
    const { data: orderItems } = await supabase
      .from('order_items')
      .select('id')
      .eq('product_id', id)
      .limit(1)

    if (orderItems && orderItems.length > 0) {
      return NextResponse.json(
        { error: 'Produkt kann nicht gelöscht werden, da es in Bestellungen verwendet wird' },
        { status: 400 }
      )
    }

    // Delete product
    const { error } = await supabase
      .from('products')
      .delete()
      .eq('id', id)

    if (error) {
      console.error('Error deleting product:', error)
      return NextResponse.json(
        { error: 'Fehler beim Löschen des Produkts' },
        { status: 500 }
      )
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error in product delete:', error)
    return NextResponse.json(
      { error: 'Interner Serverfehler' },
      { status: 500 }
    )
  }
}
